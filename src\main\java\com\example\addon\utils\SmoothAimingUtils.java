package com.example.addon.utils;

import net.minecraft.util.math.MathHelper;
import net.minecraft.util.math.Vec3d;
import net.minecraft.entity.Entity;

/**
 * Utility class for smooth aiming implementations.
 * Provides methods for calculating smooth rotations with configurable speed and interpolation.
 */
public class SmoothAimingUtils {
    
    /**
     * Calculate smooth rotations to a target position with configurable speed.
     * 
     * @param currentYaw Current yaw angle of the player
     * @param currentPitch Current pitch angle of the player
     * @param targetPos Target position to aim at
     * @param playerPos Player's eye position
     * @param aimSpeed Speed factor for aiming (0.0 to 1.0)
     * @param deltaTime Time elapsed since last frame
     * @return Array containing [yaw, pitch] for smooth rotation
     */
    public static float[] calculateSmoothRotations(float currentYaw, float currentPitch, Vec3d targetPos, Vec3d playerPos, double aimSpeed, float deltaTime) {
        // Calculate direction vector
        double dx = targetPos.x - playerPos.x;
        double dy = targetPos.y - playerPos.y;
        double dz = targetPos.z - playerPos.z;
        
        // Calculate target angles
        double distanceXZ = Math.sqrt(dx * dx + dz * dz);
        float targetYaw = (float) Math.toDegrees(Math.atan2(dz, dx)) - 90.0F;
        float targetPitch = (float) Math.toDegrees(-Math.atan2(dy, distanceXZ));
        
        // Normalize angles
        targetYaw = normalizeAngle(targetYaw);
        targetPitch = MathHelper.clamp(targetPitch, -90.0F, 90.0F);
        currentYaw = normalizeAngle(currentYaw);

        // Calculate angle differences
        float deltaYaw = targetYaw - currentYaw;
        float deltaPitch = targetPitch - currentPitch;
        
        // Normalize yaw difference to [-180, 180]
        if (deltaYaw > 180) deltaYaw -= 360;
        if (deltaYaw < -180) deltaYaw += 360;
        
        // Apply smoothing based on aim speed and delta time
        // Clamp deltaTime to prevent issues with very high or low frame times
        float clampedDeltaTime = Math.max(0.0f, Math.min(deltaTime, 0.1f));
        float frameAimSpeed = (float) (aimSpeed * clampedDeltaTime * 60);
        float smoothedYaw = currentYaw + deltaYaw * frameAimSpeed;
        float smoothedPitch = currentPitch + deltaPitch * frameAimSpeed;
        
        // Clamp pitch to valid range
        smoothedPitch = MathHelper.clamp(smoothedPitch, -90.0F, 90.0F);
        
        return new float[] {smoothedYaw, smoothedPitch};
    }
    
    /**
     * Calculate smooth rotations with exponential smoothing for even smoother aiming.
     * 
     * @param currentYaw Current yaw angle of the player
     * @param currentPitch Current pitch angle of the player
     * @param targetPos Target position to aim at
     * @param playerPos Player's eye position
     * @param aimSpeed Speed factor for aiming (0.0 to 1.0)
     * @param deltaTime Time elapsed since last frame
     * @param smoothingFactor Exponential smoothing factor (0.0 to 1.0, higher = smoother)
     * @return Array containing [yaw, pitch] for smooth rotation
     */
    public static float[] calculateExponentialSmoothRotations(float currentYaw, float currentPitch, Vec3d targetPos, Vec3d playerPos, double aimSpeed, float deltaTime, double smoothingFactor) {
        // Calculate direction vector
        double dx = targetPos.x - playerPos.x;
        double dy = targetPos.y - playerPos.y;
        double dz = targetPos.z - playerPos.z;
        
        // Calculate target angles
        double distanceXZ = Math.sqrt(dx * dx + dz * dz);
        float targetYaw = (float) Math.toDegrees(Math.atan2(dz, dx)) - 90.0F;
        float targetPitch = (float) Math.toDegrees(-Math.atan2(dy, distanceXZ));
        
        // Normalize angles
        targetYaw = normalizeAngle(targetYaw);
        targetPitch = MathHelper.clamp(targetPitch, -90.0F, 90.0F);
        currentYaw = normalizeAngle(currentYaw);

        // Calculate angle differences
        float deltaYaw = targetYaw - currentYaw;
        float deltaPitch = targetPitch - currentPitch;
        
        // Normalize yaw difference to [-180, 180]
        if (deltaYaw > 180) deltaYaw -= 360;
        if (deltaYaw < -180) deltaYaw += 360;
        
        // Apply smoothing based on aim speed and delta time
        // Clamp deltaTime to prevent issues with very high or low frame times
        float clampedDeltaTime = Math.max(0.0f, Math.min(deltaTime, 0.1f));
        float frameAimSpeed = (float) (aimSpeed * clampedDeltaTime * 60);
        
        // Apply exponential smoothing
        float expFactor = (float) smoothingFactor;
        // Exponential smoothing: new = old + factor * (target - old)
        float smoothedYaw = currentYaw + (deltaYaw * frameAimSpeed) * (1 - expFactor);
        float smoothedPitch = currentPitch + (deltaPitch * frameAimSpeed) * (1 - expFactor);
        
        // Clamp pitch to valid range
        smoothedPitch = MathHelper.clamp(smoothedPitch, -90.0F, 90.0F);
        
        return new float[] {smoothedYaw, smoothedPitch};
    }

    /**
     * Normalize angle to [0, 360) range
     * 
     * @param angle Angle to normalize
     * @return Normalized angle
     */
    public static float normalizeAngle(float angle) {
        angle %= 360.0F;
        if (angle >= 0.0F) {
            return angle;
        } else {
            return 360.0F + angle;
        }
    }
    
    /**
     * Calculate rotation speed needed to reach target angles within a specific time
     * 
     * @param currentYaw Current yaw angle
     * @param currentPitch Current pitch angle
     * @param targetYaw Target yaw angle
     * @param targetPitch Target pitch angle
     * @param rotationTime Time in seconds to reach target
     * @param deltaTime Time elapsed since last frame
     * @return Array containing [yawSpeed, pitchSpeed] for smooth rotation
     */
    public static float[] calculateRotationSpeed(float currentYaw, float currentPitch, float targetYaw, float targetPitch, double rotationTime, float deltaTime) {
        // Normalize angles
        targetYaw = normalizeAngle(targetYaw);
        targetPitch = MathHelper.clamp(targetPitch, -90.0F, 90.0F);
        currentYaw = normalizeAngle(currentYaw);

        // Calculate angle differences
        float deltaYaw = targetYaw - currentYaw;
        float deltaPitch = targetPitch - currentPitch;
        
        // Normalize yaw difference to [-180, 180]
        if (deltaYaw > 180) deltaYaw -= 360;
        if (deltaYaw < -180) deltaYaw += 360;
        
        // Calculate speed needed to reach target in specified time
        float yawSpeed = (float) (deltaYaw / (rotationTime * 20)); // 20 TPS
        float pitchSpeed = (float) (deltaPitch / (rotationTime * 20)); // 20 TPS
        
        return new float[] {yawSpeed, pitchSpeed};
    }

    /**
     * Calculate smooth rotations to a randomized target position within an entity's bounds.
     * Combines smooth aiming with natural randomization for more human-like targeting.
     *
     * @param currentYaw Current yaw angle of the player
     * @param currentPitch Current pitch angle of the player
     * @param targetEntity Target entity to aim at
     * @param playerPos Player's eye position
     * @param aimSpeed Speed factor for aiming (0.0 to 1.0)
     * @param deltaTime Time elapsed since last frame
     * @param randomizationStrength How much randomization to apply (0.0 to 1.0)
     * @param proximityBias How much to prefer points closer to current crosshair (0.0 to 1.0)
     * @return Array containing [yaw, pitch] for smooth randomized rotation
     */
    public static float[] calculateRandomizedSmoothRotations(float currentYaw, float currentPitch, Entity targetEntity, Vec3d playerPos, double aimSpeed, float deltaTime, double randomizationStrength, double proximityBias) {
        if (targetEntity == null || playerPos == null) {
            return new float[] {currentYaw, currentPitch};
        }

        // Get randomized target position
        Vec3d randomizedTargetPos = RandomizedAimingUtils.getRandomizedTargetPosition(
            targetEntity, playerPos, randomizationStrength, proximityBias
        );

        if (randomizedTargetPos == null) {
            return new float[] {currentYaw, currentPitch};
        }

        // Use existing smooth rotation calculation with randomized target
        return calculateSmoothRotations(currentYaw, currentPitch, randomizedTargetPos, playerPos, aimSpeed, deltaTime);
    }

    /**
     * Calculate smooth rotations with exponential smoothing and randomization.
     *
     * @param currentYaw Current yaw angle of the player
     * @param currentPitch Current pitch angle of the player
     * @param targetEntity Target entity to aim at
     * @param playerPos Player's eye position
     * @param aimSpeed Speed factor for aiming (0.0 to 1.0)
     * @param deltaTime Time elapsed since last frame
     * @param smoothingFactor Exponential smoothing factor (0.0 to 1.0)
     * @param randomizationStrength How much randomization to apply (0.0 to 1.0)
     * @param proximityBias How much to prefer points closer to current crosshair (0.0 to 1.0)
     * @return Array containing [yaw, pitch] for smooth randomized rotation
     */
    public static float[] calculateRandomizedExponentialSmoothRotations(float currentYaw, float currentPitch, Entity targetEntity, Vec3d playerPos, double aimSpeed, float deltaTime, double smoothingFactor, double randomizationStrength, double proximityBias) {
        if (targetEntity == null || playerPos == null) {
            return new float[] {currentYaw, currentPitch};
        }

        // Get randomized target position
        Vec3d randomizedTargetPos = RandomizedAimingUtils.getRandomizedTargetPosition(
            targetEntity, playerPos, randomizationStrength, proximityBias
        );

        if (randomizedTargetPos == null) {
            return new float[] {currentYaw, currentPitch};
        }

        // Use existing exponential smooth rotation calculation with randomized target
        return calculateExponentialSmoothRotations(currentYaw, currentPitch, randomizedTargetPos, playerPos, aimSpeed, deltaTime, smoothingFactor);
    }

    /**
     * Calculate smart randomized smooth rotations with body part targeting.
     *
     * @param currentYaw Current yaw angle of the player
     * @param currentPitch Current pitch angle of the player
     * @param targetEntity Target entity to aim at
     * @param playerPos Player's eye position
     * @param aimSpeed Speed factor for aiming (0.0 to 1.0)
     * @param deltaTime Time elapsed since last frame
     * @param randomizationStrength How much randomization to apply (0.0 to 1.0)
     * @param proximityBias How much to prefer points closer to current crosshair (0.0 to 1.0)
     * @param preferVitalAreas Whether to prefer head/torso areas
     * @return Array containing [yaw, pitch] for smart randomized rotation
     */
    public static float[] calculateSmartRandomizedSmoothRotations(float currentYaw, float currentPitch, Entity targetEntity, Vec3d playerPos, double aimSpeed, float deltaTime, double randomizationStrength, double proximityBias, boolean preferVitalAreas) {
        if (targetEntity == null || playerPos == null) {
            return new float[] {currentYaw, currentPitch};
        }

        // Get smart randomized target position
        Vec3d randomizedTargetPos = RandomizedAimingUtils.getSmartRandomizedTargetPosition(
            targetEntity, playerPos, randomizationStrength, proximityBias, preferVitalAreas
        );

        if (randomizedTargetPos == null) {
            return new float[] {currentYaw, currentPitch};
        }

        // Use existing smooth rotation calculation with smart randomized target
        return calculateSmoothRotations(currentYaw, currentPitch, randomizedTargetPos, playerPos, aimSpeed, deltaTime);
    }

    /**
     * Add subtle randomization to existing rotation calculations.
     * This method can be used to add small random variations to any aiming system.
     *
     * @param rotations Array containing [yaw, pitch] rotations
     * @param randomizationAmount Maximum random offset in degrees
     * @return Array containing [randomized_yaw, randomized_pitch]
     */
    public static float[] addRandomizationToRotations(float[] rotations, double randomizationAmount) {
        if (rotations == null || rotations.length < 2 || randomizationAmount <= 0.0) {
            return rotations;
        }

        return RandomizedAimingUtils.addRandomizationToRotation(rotations[0], rotations[1], randomizationAmount);
    }
}