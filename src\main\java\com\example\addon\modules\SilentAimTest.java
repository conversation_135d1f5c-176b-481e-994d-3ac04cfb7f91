package com.example.addon.modules;

import com.example.addon.AddonTemplate;
import meteordevelopment.meteorclient.events.packets.PacketEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.meteorclient.utils.entity.TargetUtils;
import meteordevelopment.meteorclient.utils.entity.SortPriority;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityType;
import net.minecraft.network.packet.c2s.play.PlayerMoveC2SPacket;
import net.minecraft.network.packet.c2s.play.PlayerInteractEntityC2SPacket;
import net.minecraft.network.packet.c2s.play.PlayerActionC2SPacket;
import net.minecraft.network.packet.c2s.play.PlayerInteractBlockC2SPacket;
import net.minecraft.network.packet.c2s.play.PlayerInteractItemC2SPacket;
import net.minecraft.util.math.MathHelper;
import net.minecraft.util.math.Vec3d;
import net.minecraft.util.hit.HitResult;

import java.util.Set;
import java.util.function.Predicate;

import static meteordevelopment.meteorclient.MeteorClient.mc;

public class SilentAimTest extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();

    private final Setting<Double> range = sgGeneral.add(new DoubleSetting.Builder()
        .name("range")
        .description("How far to check for targets.")
        .defaultValue(50.0)
        .range(3.0, 100.0)
        .sliderRange(3.0, 100.0)
        .build()
    );

    private final Setting<Boolean> movementCorrection = sgGeneral.add(new BoolSetting.Builder()
        .name("movement-correction")
        .description("Corrects your movement to align with the server-side angle.")
        .defaultValue(true)
        .build()
    );



    private Entity target = null;
    private float serverYaw, serverPitch;
    private boolean hasValidRotation = false;
    private boolean sendingPacket = false;
    
    // Add a method to check if another module has taken action this tick
    private boolean hasAnotherModuleTakenAction() {
        // Check if MaceAura has taken action
        try {
            Class<?> maceAuraClass = Class.forName("com.example.addon.modules.MaceAura");
            java.lang.reflect.Field actionTakenField = maceAuraClass.getDeclaredField("actionTakenThisTick");
            actionTakenField.setAccessible(true);
            return actionTakenField.getBoolean(null);
        } catch (Exception e) {
            // If we can't check, assume no conflict
            return false;
        }
    }

    public SilentAimTest() {
        super(AddonTemplate.CATEGORY, "silent-aim-test", "A robust test for silent aim with F5 head turning and movement correction.");
    }

    @Override
    public void onDeactivate() {
        target = null;
        hasValidRotation = false;
        sendingPacket = false;
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        // Check if another module has already taken action this tick
        if (hasAnotherModuleTakenAction()) {
            return;
        }
        
        if (mc.player == null || mc.world == null) {
            target = null;
            hasValidRotation = false;
            return;
        }

        findTarget();

        if (target != null) {
            calculateRotations(target);
            hasValidRotation = true;
        } else {
            hasValidRotation = false;
        }
    }

    @EventHandler
    private void onPacketSend(PacketEvent.Send event) {
        if (sendingPacket || !hasValidRotation || target == null) return;

        // Modify movement packets to use server-side rotation AND corrected movement
        if (event.packet instanceof PlayerMoveC2SPacket packet) {
            if (packet instanceof PlayerMoveC2SPacket.LookAndOnGround lookPacket) {
                event.cancel();
                sendingPacket = true;
                mc.player.networkHandler.sendPacket(new PlayerMoveC2SPacket.LookAndOnGround(
                    serverYaw, serverPitch, lookPacket.isOnGround(), lookPacket.horizontalCollision()
                ));
                sendingPacket = false;
            } else if (packet instanceof PlayerMoveC2SPacket.Full fullPacket) {
                event.cancel();
                sendingPacket = true;
                if (movementCorrection.get()) {
                    Vec3d correctedPos = getCorrectedPosition(fullPacket.getX(mc.player.getX()), fullPacket.getY(mc.player.getY()), fullPacket.getZ(mc.player.getZ()));
                    mc.player.networkHandler.sendPacket(new PlayerMoveC2SPacket.Full(
                        correctedPos.x, correctedPos.y, correctedPos.z,
                        serverYaw, serverPitch,
                        fullPacket.isOnGround(), fullPacket.horizontalCollision()
                    ));
                } else {
                    mc.player.networkHandler.sendPacket(new PlayerMoveC2SPacket.Full(
                        fullPacket.getX(mc.player.getX()), fullPacket.getY(mc.player.getY()), fullPacket.getZ(mc.player.getZ()),
                        serverYaw, serverPitch,
                        fullPacket.isOnGround(), fullPacket.horizontalCollision()
                    ));
                }
                sendingPacket = false;
            } else if (packet instanceof PlayerMoveC2SPacket.PositionAndOnGround posPacket) {
                event.cancel();
                sendingPacket = true;
                if (movementCorrection.get()) {
                    Vec3d correctedPos = getCorrectedPosition(posPacket.getX(mc.player.getX()), posPacket.getY(mc.player.getY()), posPacket.getZ(mc.player.getZ()));
                    mc.player.networkHandler.sendPacket(new PlayerMoveC2SPacket.Full(
                        correctedPos.x, correctedPos.y, correctedPos.z,
                        serverYaw, serverPitch,
                        posPacket.isOnGround(), posPacket.horizontalCollision()
                    ));
                } else {
                    mc.player.networkHandler.sendPacket(new PlayerMoveC2SPacket.Full(
                        posPacket.getX(mc.player.getX()), posPacket.getY(mc.player.getY()), posPacket.getZ(mc.player.getZ()),
                        serverYaw, serverPitch,
                        posPacket.isOnGround(), posPacket.horizontalCollision()
                    ));
                }
                sendingPacket = false;
            }
        }

        // Redirect attacks to target
        if (event.packet instanceof PlayerInteractEntityC2SPacket) {
            event.cancel();
            sendingPacket = true;
            mc.player.networkHandler.sendPacket(PlayerInteractEntityC2SPacket.attack(target, mc.player.isSneaking()));
            sendingPacket = false;
        }
    }

    private Vec3d getCorrectedPosition(double x, double y, double z) {
        float forward = (float) mc.player.input.getMovementInput().y;
        float strafe = (float) mc.player.input.getMovementInput().x;

        if (forward == 0.0f && strafe == 0.0f) {
            return new Vec3d(x, y, z); // No movement, return current position
        }

        // Calculate movement based on SERVER-SIDE rotation, not client camera
        float clientYaw = mc.player.getYaw();
        float yawDifference = MathHelper.wrapDegrees(serverYaw - clientYaw);

        Vec3d moveInput = new Vec3d(strafe, 0, forward).normalize();
        Vec3d correctedMovement = moveInput.rotateY((float) -Math.toRadians(yawDifference));

        float speed = (float) (mc.player.getMovementSpeed() * (forward != 0.0f && strafe != 0.0f ? 0.7071 : 1.0));

        // Apply the corrected movement to current position
        return new Vec3d(
            x + correctedMovement.x * speed,
            y, // Don't modify Y position
            z + correctedMovement.z * speed
        );
    }

    private void findTarget() {
        target = null;

        Targets targetsModule = Modules.get().get(Targets.class);
        if (targetsModule == null || !targetsModule.isActive()) {
            return;
        }

        Predicate<Entity> targetPredicate = entity -> {
            if (entity.equals(mc.player)) return false;
            
            // Use the targetsModule variable from the outer scope instead of redeclaring it
            if (targetsModule == null || !targetsModule.isActive()) {
                return false;
            }
            
            // Use the new shouldTarget method to check if we should target this entity
            return targetsModule.shouldTarget(entity);
        };

        Entity foundTarget = TargetUtils.get(targetPredicate, SortPriority.LowestDistance);

        if (foundTarget != null) {
            target = foundTarget;
        }
    }

    private void calculateRotations(Entity targetEntity) {
        Vec3d targetPos = targetEntity.getBoundingBox().getCenter();
        Vec3d playerPos = mc.player.getEyePos();
        double dx = targetPos.x - playerPos.x;
        double dy = targetPos.y - playerPos.y;
        double dz = targetPos.z - playerPos.z;
        double distanceXZ = Math.sqrt(dx * dx + dz * dz);

        serverYaw = (float) Math.toDegrees(Math.atan2(dz, dx)) - 90.0F;
        serverPitch = (float) Math.toDegrees(-Math.atan2(dy, distanceXZ));
    }
}