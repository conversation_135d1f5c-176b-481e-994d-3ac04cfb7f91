package com.example.addon.modules;

import com.example.addon.AddonTemplate;
import com.example.addon.utils.EnchantmentUtils;
import com.example.addon.utils.SmoothAimingUtils;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.meteorclient.utils.entity.TargetUtils;
import meteordevelopment.meteorclient.utils.entity.SortPriority;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.enchantment.Enchantments;
import net.minecraft.entity.Entity;
import net.minecraft.entity.LivingEntity;
import net.minecraft.item.AxeItem;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.network.packet.c2s.play.PlayerMoveC2SPacket;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.MathHelper;
import net.minecraft.util.math.Vec3d;
import net.minecraft.util.Hand;

import java.util.function.Predicate;

import java.lang.reflect.Method;

import static meteordevelopment.meteorclient.MeteorClient.mc;

public class StunSlam extends Module {
    private enum State {
        IDLE, AXE_ATTACK_PENDING, AXE_ATTACKED, AXE_SPAM, MACE_SWAPPED, MACE_ATTACKED
    }

    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgAiming = settings.createGroup("Aiming");


    private final Setting<Double> minFallDistance = sgGeneral.add(new DoubleSetting.Builder().name("min-fall-distance").description("The minimum distance you must be falling for the combo to activate.").defaultValue(1.5).range(0.5, 10.0).sliderRange(0.5, 10.0).build());
    private final Setting<Boolean> requireShield = sgGeneral.add(new BoolSetting.Builder().name("require-shield").description("Only attack players that are actively blocking with a shield.").defaultValue(true).build());
    private final Setting<Boolean> timedMode = sgGeneral.add(new BoolSetting.Builder().name("timed-mode").description("Use timed attacks instead of checking if the shield is down.").defaultValue(false).build());
    private final Setting<Integer> axeAttacks = sgGeneral.add(new IntSetting.Builder().name("axe-attacks").description("Number of axe attacks in timed mode.").defaultValue(3).range(1, 10).sliderRange(1, 10).visible(timedMode::get).build());
    private final Setting<Integer> maxAxeHits = sgGeneral.add(new IntSetting.Builder().name("max-axe-hits").description("Maximum axe attacks before switching to mace (prevents infinite axe spam).").defaultValue(2).range(1, 5).sliderRange(1, 5).visible(() -> !timedMode.get()).build());
    // Removed delayTicks setting - combo now runs at maximum speed for effectiveness
    private final Setting<Boolean> silentAim = sgAiming.add(new BoolSetting.Builder().name("silent-aim").description("Aims on the server without moving your camera, with F5 head turning and movement correction.").defaultValue(true).build());
    private final Setting<Double> aimSpeed = sgAiming.add(new DoubleSetting.Builder()
        .name("aim-speed")
        .description("The speed at which the player aims at the target.")
        .defaultValue(1.0d)
        .range(0.1d, 1.0d)
        .build()
    );

    private final Setting<Double> aimRange = sgAiming.add(new DoubleSetting.Builder()
        .name("aim-range")
        .description("The maximum range at which to aim at a target.")
        .defaultValue(4.0)
        .range(0.0, 10.0)
        .sliderRange(0.0, 10.0)
        .build()
    );

    private final Setting<Double> fov = sgAiming.add(new DoubleSetting.Builder() // New FOV setting
        .name("fov")
        .description("The field of view (in degrees) within which to target entities.")
        .defaultValue(90.0)
        .min(0.0)
        .max(360.0)
        .sliderMin(0.0)
        .sliderMax(360.0)
        .build()
    );

    // --- New setting for exponential smoothing ---
    private final Setting<Double> exponentialSmoothing = sgAiming.add(new DoubleSetting.Builder()
        .name("exponential-smoothing")
        .description("Exponential smoothing factor for aiming (0.0 = no smoothing, 1.0 = maximum smoothing).")
        .defaultValue(0.0)
        .min(0.0)
        .max(1.0)
        .sliderMin(0.0)
        .sliderMax(1.0)
        .build()
    );
    // --- End new setting ---

    // Add a new setting for attack range
    private final Setting<Double> attackRange = sgGeneral.add(new DoubleSetting.Builder()
        .name("attack-range")
        .description("The maximum range at which to attack a target.")
        .defaultValue(3.0)
        .range(0.0, 6.0)
        .sliderRange(0.0, 6.0)
        .build()
    );
    
    // --- New setting for keep target mode ---
    private final Setting<Boolean> keepTarget = sgGeneral.add(new BoolSetting.Builder()
        .name("keep-target")
        .description("Keep the same target until they die or move too far away.")
        .defaultValue(false)
        .build()
    );

    private final Setting<Double> keepTargetDistance = sgGeneral.add(new DoubleSetting.Builder()
        .name("keep-target-distance")
        .description("Maximum distance to keep the target.")
        .defaultValue(10.0)
        .min(5.0)
        .sliderMax(20.0)
        .visible(keepTarget::get)
        .build()
    );
    // --- End new setting ---

    private LivingEntity target = null;
    private int originalSlot = -1;
    private int actionTimer = 0;
    private State currentState = State.IDLE;
    private Method doAttackMethod;
    private float serverYaw, serverPitch;
    private boolean shouldAim = false;
    private int attackTimer = 0;
    private boolean isAttackingMace = false;
    private boolean hasSwappedToAxe = false; // Track if we've swapped to axe
    private int axeAttackCount = 0; // Track number of axe attacks in timed mode
    private boolean targetInRange = false; // Track if target is in range

    // Cache reflection objects for performance
    private static Class<?> cachedMaceAuraClass;
    private static java.lang.reflect.Field cachedActionTakenField;
    private static boolean reflectionInitialized = false;

    // Cache frequently used calculations
    private Vec3d cachedPlayerPos;
    private int lastPlayerPosUpdate = -1;
    private int tickCounter = 0;

    public StunSlam() {
        super(AddonTemplate.CATEGORY, "stun-slam", "Performs a frame-perfect, precise combo on shielded players while falling.");
        initializeAttackMethod();
        initializeReflectionCache();
    }

    // Initialize reflection cache once for better performance
    private static void initializeReflectionCache() {
        if (!reflectionInitialized) {
            try {
                cachedMaceAuraClass = Class.forName("com.example.addon.modules.MaceAura");
                cachedActionTakenField = cachedMaceAuraClass.getDeclaredField("actionTakenThisTick");
                cachedActionTakenField.setAccessible(true);
                reflectionInitialized = true;
            } catch (Exception e) {
                // Ignore if we can't initialize reflection cache
                reflectionInitialized = false;
            }
        }
    }

    private void initializeAttackMethod() {
        try {
            // Try different method names - 1.21.5 may use a different obfuscated name
            try {
                doAttackMethod = mc.getClass().getDeclaredMethod("method_1536");
            } catch (NoSuchMethodException e1) {
                try {
                    // Try the actual method name (in development environment)
                    doAttackMethod = mc.getClass().getDeclaredMethod("doAttack");
                } catch (NoSuchMethodException e2) {
                    // If all attempts fail
                    error("Failed to find Minecraft's attack method via reflection. " +
                        "Module will use alternative attack method.");
                    return;
                }
            }
            
            // Make the method accessible
            doAttackMethod.setAccessible(true);
            info("Successfully found attack method via reflection for StunSlam.");
        } catch (Exception e) {
            error("StunSlam: Failed to initialize attack method: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void onActivate() { reset(); }
    @Override
    public void onDeactivate() {
        if (originalSlot != -1 && mc.player != null) InvUtils.swap(originalSlot, false);
        reset();
    }

    private void reset() {
        target = null;
        originalSlot = -1;
        actionTimer = 0;
        currentState = State.IDLE;
        shouldAim = false;
        attackTimer = 0;
        isAttackingMace = false;
        hasSwappedToAxe = false;
        axeAttackCount = 0;
        targetInRange = false;
    }

    private boolean isHoldingAxe() {
        if (mc.player == null) return false;
        return mc.player.getMainHandStack().getItem() instanceof AxeItem;
    }

    // Calculate Euclidean distance between player and target (optimized with caching)
    private double getEuclideanDistance(LivingEntity target) {
        if (mc.player == null || target == null) return Double.MAX_VALUE;

        // Cache player position for the current tick to avoid recalculating
        int currentTick = mc.player.age;
        if (cachedPlayerPos == null || lastPlayerPosUpdate != currentTick) {
            cachedPlayerPos = mc.player.getPos().add(0, mc.player.getEyeHeight(mc.player.getPose()), 0);
            lastPlayerPosUpdate = currentTick;
        }

        Vec3d targetPos = target.getPos().add(0, target.getEyeHeight(target.getPose()), 0);
        return cachedPlayerPos.distanceTo(targetPos);
    }

    // Check if target is in attack range
    private boolean checkTargetInRange() {
        if (target == null || mc.player == null) return false;
        return getEuclideanDistance(target) <= attackRange.get();
    }

    private void attackTarget() {
        if (doAttackMethod == null) {
            // Re-initialize if it's null - may have been reset
            initializeAttackMethod();
            
            // If still null after re-initialization, use alternative method
            if (doAttackMethod == null) {
                error("StunSlam: Attack method is null. Using alternative attack method.");
                // Fall back to direct interaction
                if (mc.interactionManager != null && target != null) {
                    mc.interactionManager.attackEntity(mc.player, target);
                    mc.player.swingHand(Hand.MAIN_HAND);
                }
                return;
            }
        }
        
        try {
            doAttackMethod.invoke(mc);
        } catch (Exception e) {
            error("StunSlam: Failed to invoke attack method: " + e.getMessage());
            // Fall back to direct interaction
            if (mc.interactionManager != null && target != null) {
                mc.interactionManager.attackEntity(mc.player, target);
                mc.player.swingHand(Hand.MAIN_HAND);
            }
        }
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (mc.player == null || mc.world == null) { reset(); return; }

        tickCounter++;

        // Check if another module has already taken action this tick
        if (hasAnotherModuleTakenAction()) {
            log("Another module has already taken action this tick. Skipping StunSlam.");
            return;
        }

        // Reset if on ground and not idle
        if (mc.player.isOnGround() && currentState != State.IDLE) {
            if (originalSlot != -1) InvUtils.swap(originalSlot, false);
            reset();
            return;
        }

        Targets targetsModule = Modules.get().get(Targets.class);

        // Preemptively swap to axe before falling
        if (currentState == State.IDLE && !hasSwappedToAxe) {
            // Check if we have a valid target within aim range
            if (findValidTargetWithKeepTarget(targetsModule)) {
                // Check if we're not already holding an axe
                if (!isHoldingAxe()) {
                    FindItemResult axeResult = InvUtils.findInHotbar(itemStack -> itemStack.getItem() instanceof AxeItem);
                    if (axeResult.found()) {
                        originalSlot = InvUtils.findInHotbar(mc.player.getMainHandStack().getItem()).slot();
                        InvUtils.swap(axeResult.slot(), false);
                        hasSwappedToAxe = true;
                    }
                } else {
                    hasSwappedToAxe = true;
                }
            }
        }

        // Check if falling and if fall distance is met
        if (mc.player.fallDistance < minFallDistance.get()) {
            // Don't reset if we're just waiting to fall, but don't proceed with the combo either
            if (currentState != State.IDLE && currentState != State.AXE_ATTACK_PENDING) {
                if (originalSlot != -1) InvUtils.swap(originalSlot, false);
                reset();
            }
            return;
        }

        if (target != null && shouldAim && !mc.player.isOnGround() && getEuclideanDistance(target) <= aimRange.get()) {
            if (silentAim.get()) {
                applyMovementCorrection();
                mc.player.networkHandler.sendPacket(new PlayerMoveC2SPacket.LookAndOnGround(serverYaw, serverPitch, mc.player.isOnGround(), false));
            }
        }
        if (targetsModule == null || !targetsModule.isActive()) {
            if (currentState != State.IDLE) {
                if (originalSlot != -1) InvUtils.swap(originalSlot, false);
                reset();
            }
            return;
        }

        // Find a valid target with keepTarget logic (optimize frequency when no target)
        if (target != null || tickCounter % 3 == 0) {
            findValidTargetWithKeepTarget(targetsModule);
        }

        if (currentState != State.IDLE && (target == null || !target.isAlive() || getEuclideanDistance(target) > targetsModule.range.get() + 1)) {
            if (originalSlot != -1) InvUtils.swap(originalSlot, false);
            reset();
            return;
        }

        // Update target in range status
        targetInRange = checkTargetInRange();

        // No delays - combo runs at maximum speed for effectiveness

        // Only proceed with combo if all conditions are met
        if (currentState == State.IDLE) {
            if (target != null && isHoldingAxe() && mc.player.fallDistance >= minFallDistance.get()) {
                // Check if target is within attack range using Euclidean distance
                if (targetInRange) {
                    shouldAim = true;
                    FindItemResult slotResult = InvUtils.findInHotbar(mc.player.getMainHandStack().getItem());
                    if (!slotResult.found()) { reset(); return; }
                    originalSlot = slotResult.slot();
                    currentState = State.AXE_ATTACK_PENDING;
                    // Execute attack on the next tick - combo optimized for maximum speed
                }
            }
        }

        switch (currentState) {
            case AXE_ATTACK_PENDING:
                // Execute the axe attack on this tick
                attackTarget();
                axeAttackCount++;
                
                // Mark that this module is taking action this tick using cached reflection
                if (reflectionInitialized && cachedActionTakenField != null) {
                    try {
                        cachedActionTakenField.setBoolean(null, true);
                    } catch (Exception e) {
                        // Ignore if we can't set the flag
                    }
                }
                
                // Progress state for next tick based on conditions
                if (timedMode.get()) {
                    if (axeAttackCount < axeAttacks.get()) {
                        currentState = State.AXE_SPAM;
                    } else {
                        currentState = State.AXE_ATTACKED;
                    }
                } else {
                    // Normal mode - be more aggressive, only spam if shield is still up AND we haven't hit the limit
                    if (target != null && target.isBlocking() && requireShield.get() && axeAttackCount < maxAxeHits.get()) {
                        currentState = State.AXE_SPAM;
                    } else {
                        // Proceed to mace attack - either shield is down or we've hit the limit
                        currentState = State.AXE_ATTACKED;
                    }
                }
                break;
                
            case AXE_SPAM:
                if (timedMode.get()) {
                    // In timed mode, continue attacking regardless of shield status
                    if (axeAttackCount < axeAttacks.get()) {
                        attackTarget();
                        axeAttackCount++;
                    } else {
                        // Switch to mace and attack immediately for faster combo
                        shouldAim = true;
                        FindItemResult maceResult = InvUtils.findInHotbar(s -> s.getItem() == Items.MACE && EnchantmentUtils.hasEnchantment(s, Enchantments.DENSITY));
                        if (!maceResult.found()) {
                            if (originalSlot != -1) InvUtils.swap(originalSlot, false);
                            reset(); break;
                        }
                        InvUtils.swap(maceResult.slot(), false);

                        // Attack immediately after swapping for maximum speed
                        if (targetInRange) {
                            attackTarget();
                            currentState = State.MACE_ATTACKED;
                            isAttackingMace = true;
                        } else {
                            if (originalSlot != -1) InvUtils.swap(originalSlot, false);
                            reset();
                        }
                    }
                } else {
                    // Normal mode - check if target is still blocking with configurable limits
                    if (target != null && target.isBlocking() && requireShield.get() && axeAttackCount < maxAxeHits.get()) {
                        attackTarget();
                        axeAttackCount++; // Increment counter to prevent infinite spam
                        info("StunSlam: Axe attack #" + axeAttackCount + " (target still blocking)");
                    } else {
                        // Either shield is down, we've hit the limit, or requireShield is false - proceed to mace
                        info("StunSlam: Switching to mace (axe attacks: " + axeAttackCount + ", target blocking: " + (target != null && target.isBlocking()) + ")");
                        // Switch to mace and attack immediately for faster combo
                        shouldAim = true;
                        FindItemResult maceResult = InvUtils.findInHotbar(s -> s.getItem() == Items.MACE && EnchantmentUtils.hasEnchantment(s, Enchantments.DENSITY));
                        if (!maceResult.found()) {
                            if (originalSlot != -1) InvUtils.swap(originalSlot, false);
                            reset(); break;
                        }
                        InvUtils.swap(maceResult.slot(), false);

                        // Attack immediately after swapping for maximum speed
                        if (targetInRange) {
                            attackTarget();
                            currentState = State.MACE_ATTACKED;
                            isAttackingMace = true;
                            info("StunSlam: Mace attack executed - combo complete!");
                        } else {
                            if (originalSlot != -1) InvUtils.swap(originalSlot, false);
                            reset();
                        }
                    }
                }
                break;
                
            case AXE_ATTACKED:
                // Switch to mace and attack immediately on this tick for faster combo
                shouldAim = true;
                FindItemResult maceResult = InvUtils.findInHotbar(s -> s.getItem() == Items.MACE && EnchantmentUtils.hasEnchantment(s, Enchantments.DENSITY));
                if (!maceResult.found()) {
                    if (originalSlot != -1) InvUtils.swap(originalSlot, false);
                    reset(); break;
                }
                InvUtils.swap(maceResult.slot(), false);

                // Attack immediately after swapping for maximum speed
                if (targetInRange) {
                    attackTarget();
                    currentState = State.MACE_ATTACKED;
                    isAttackingMace = true;

                    // Mark that this module is taking action this tick using cached reflection
                    if (reflectionInitialized && cachedActionTakenField != null) {
                        try {
                            cachedActionTakenField.setBoolean(null, true);
                        } catch (Exception e) {
                            // Ignore if we can't set the flag
                        }
                    }
                } else {
                    // Target is out of range, reset
                    if (originalSlot != -1) InvUtils.swap(originalSlot, false);
                    reset();
                }
                break;

            case MACE_SWAPPED:
                // This state is now unused - combo completes in AXE_ATTACKED
                // Fallback in case we somehow get here
                shouldAim = true;
                if (mc.player.getMainHandStack().getItem() != Items.MACE) {
                    if (originalSlot != -1) InvUtils.swap(originalSlot, false);
                    reset(); break;
                }

                if (targetInRange) {
                    attackTarget();
                    currentState = State.MACE_ATTACKED;
                    isAttackingMace = true;
                } else {
                    if (originalSlot != -1) InvUtils.swap(originalSlot, false);
                    reset();
                }
                break;
                
            case MACE_ATTACKED:
                // Swap back on this tick
                if (originalSlot != -1) InvUtils.swap(originalSlot, false);
                reset();
                break;
        }
    }

    /**
     * Finds a valid target based on the targets module settings and range checks
     * @param targetsModule The targets module
     * @return true if a valid target was found, false otherwise
     */
    private boolean findValidTargetWithKeepTarget(Targets targetsModule) {
        if (targetsModule == null || !targetsModule.isActive()) {
            this.target = null;
            return false;
        }

        // If keep target mode is enabled and we have a valid current target, check if we should keep it
        if (keepTarget.get() && this.target != null) {
            // Check if target is still alive and within distance
            if (this.target.isAlive() && mc.player.distanceTo(this.target) <= keepTargetDistance.get()) {
                // Check if target is still within FOV
                if (fov.get() >= 360.0 || isWithinFOV(this.target)) {
                    // Keep the same target
                    return true;
                } else {
                    // Target is out of FOV, need to find a new target
                    this.target = null;
                }
            } else {
                // Target is dead or too far away, need to find a new target
                this.target = null;
            }
        }

        // Only find a new target if we don't have one or keepTarget is disabled
        if (this.target == null) {
            Predicate<Entity> targetPredicate = entity -> {
                if (entity.equals(mc.player)) return false;
                
                // Use the targetsModule parameter instead of redeclaring it
                if (targetsModule == null || !targetsModule.isActive()) {
                    return false;
                }
                
                // Use the new shouldTarget method to check if we should target this entity
                return targetsModule.shouldTarget(entity);
            };

            Entity foundTarget = TargetUtils.get(targetPredicate, SortPriority.LowestDistance);

            if (foundTarget instanceof LivingEntity) {
                LivingEntity livingTarget = (LivingEntity) foundTarget;
                // Check if target is within aim range using Euclidean distance
                if (getEuclideanDistance(livingTarget) <= aimRange.get()) {
                    // Check if target is within FOV
                    if (fov.get() >= 360.0 || isWithinFOV(livingTarget)) {
                        this.target = livingTarget;
                        return true;
                    }
                }
            }
            this.target = null;
        }

        return this.target != null;
    }

    // New method to check if an entity is within the FOV (optimized)
    private boolean isWithinFOV(LivingEntity entity) {
        if (mc.player == null || entity == null) return false;

        // Early exit for 360 FOV
        double fovValue = fov.get();
        if (fovValue >= 360.0) return true;

        Vec3d playerPos = mc.player.getEyePos();
        Vec3d targetPos = entity.getEyePos();

        double deltaX = targetPos.x - playerPos.x;
        double deltaY = targetPos.y - playerPos.y;
        double deltaZ = targetPos.z - playerPos.z;

        // Use squared distance for XZ to avoid sqrt when possible
        double distanceXZSquared = deltaX * deltaX + deltaZ * deltaZ;

        // Early exit if target is too close (avoid division by zero)
        if (distanceXZSquared < 0.01) return true;

        double distanceXZ = Math.sqrt(distanceXZSquared);

        // Calculate yaw and pitch to target
        float targetYaw = (float) Math.toDegrees(Math.atan2(deltaZ, deltaX)) - 90.0F;
        float targetPitch = (float) Math.toDegrees(-Math.atan2(deltaY, distanceXZ));

        // Clamp pitch to valid range
        targetPitch = MathHelper.clamp(targetPitch, -90.0F, 90.0F);

        // Get current player angles
        float currentYaw = mc.player.getYaw();
        float currentPitch = mc.player.getPitch();

        // Calculate shortest angle differences (no normalization to avoid jumps)
        float yawDiff = Math.abs(SmoothAimingUtils.getShortestAngleDifference(currentYaw, targetYaw));

        float pitchDiff = Math.abs(targetPitch - currentPitch);

        // Check if within FOV
        double halfFov = fovValue / 2.0;
        return yawDiff <= halfFov && pitchDiff <= halfFov;
    }
    
    @EventHandler
    private void onRender3d(Render3DEvent event) {
        if (mc.player == null || mc.world == null) return;

        // Only aim if all conditions are met and not flying with elytra
        if (target != null && shouldAim && currentState != State.IDLE && !isPlayerFlyingWithElytra()) {
            // Use eye position for more accurate targeting, consistent with Aimbot
            Vec3d playerPos = mc.player.getEyePos();
            Vec3d targetPos = target.getEyePos();

            // Use smooth aiming utility for better aiming
            float[] rotations;
            if (exponentialSmoothing.get() > 0.0) {
                // Use exponential smoothing for even smoother aiming
                rotations = SmoothAimingUtils.calculateExponentialSmoothRotations(
                    mc.player.getYaw(),
                    mc.player.getPitch(),
                    targetPos,
                    playerPos,
                    aimSpeed.get(),
                    (float) event.frameTime,
                    exponentialSmoothing.get()
                );
            } else {
                // Use regular smoothing
                rotations = SmoothAimingUtils.calculateSmoothRotations(
                    mc.player.getYaw(),
                    mc.player.getPitch(),
                    targetPos,
                    playerPos,
                    aimSpeed.get(),
                    (float) event.frameTime
                );
            }

            serverYaw = rotations[0];
            serverPitch = rotations[1];

            if (silentAim.get()) {
                mc.player.setHeadYaw(serverYaw);
                mc.player.setBodyYaw(serverYaw);
            } else {
                mc.player.setYaw(serverYaw);
                mc.player.setPitch(serverPitch);
            }
        }
    }

    private void applyMovementCorrection() {
        // FIX 2: Use getter methods for movement input
        float forward = (float) mc.player.input.getMovementInput().y;
        float strafe = (float) mc.player.input.getMovementInput().x;

        if (forward == 0.0f && strafe == 0.0f) return;
        float clientYaw = mc.player.getYaw();
        float yawDiff = MathHelper.wrapDegrees(serverYaw - clientYaw);
        Vec3d moveInput = new Vec3d(strafe, 0, forward).normalize();
        Vec3d correctedVec = moveInput.rotateY((float) -Math.toRadians(yawDiff));
        float speed = (float) (mc.player.getMovementSpeed() * (forward != 0.0f && strafe != 0.0f ? 0.7071 : 1.0));
        mc.player.setVelocity(correctedVec.x * speed, mc.player.getVelocity().y, correctedVec.z * speed);
    }

    /**
     * Checks if the StunSlam module is currently performing a combo attack.
     * This method is used by other modules like AutoAura to determine if they should wait
     * for the combo to complete before taking action.
     * 
     * @return true if the module is currently performing a slam combo, false otherwise
     */
    public boolean isSlamming() {
        return currentState != State.IDLE;
    }

    /**
     * Checks if the current target is within attack range.
     * This method can be called by other modules to determine if the target is in range
     * without waiting for the next tick event.
     * 
     * @return true if target is in attack range, false otherwise
     */
    public boolean isTargetWithinRange() {
        return targetInRange;
    }

    // Add a method to check if another module has taken action this tick
    private boolean hasAnotherModuleTakenAction() {
        // Check if MaceAura has taken action using cached reflection
        if (reflectionInitialized && cachedActionTakenField != null) {
            try {
                return cachedActionTakenField.getBoolean(null);
            } catch (Exception e) {
                // If we can't check, assume no conflict
                return false;
            }
        }
        return false;
    }
    
    // Add a log method for consistency with other modules
    private void log(String message) {
        // Using info instead of a custom log method
        info(message);
    }

    private boolean isPlayerFlyingWithElytra() {
        if (mc.player == null) return false;
        // Check if player has elytra equipped and is not on ground with upward or stable velocity
        ItemStack chestSlot = mc.player.getInventory().getStack(38); // Chest armor slot
        return chestSlot.getItem() == Items.ELYTRA &&
               !mc.player.isOnGround() &&
               mc.player.getVelocity().y > -0.5; // Not falling too fast (gliding)
    }

}